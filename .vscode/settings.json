{
  // Editor settings
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "explicit"
  },
  "files.insertFinalNewline": true,
  "files.trimTrailingWhitespace": true,

  // TypeScript settings
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",

  // File associations and excludes
  "files.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.vscode-test": true,
    "**/.vscode-test-web": true,
    "**/*.vsix": true
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.vscode-test": true,
    "**/.vscode-test-web": true,
    "**/*.vsix": true
  },

  // Extension development specific
  "debug.allowBreakpointsEverywhere": true,

  // Language specific settings
  "[html][javascript][json][jsonc][markdown][scss][svg][typescript][typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[markdown]": {
    "editor.wordWrap": "on"
  },
  "typescript.preferences.importModuleSpecifier": "project-relative",
  "typescript.tsdk": "node_modules/typescript/lib"
}
